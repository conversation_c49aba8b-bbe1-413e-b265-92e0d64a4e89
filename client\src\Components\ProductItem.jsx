

const ProductItem = ({ product }) => {
    return (
        <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div className="p-6">
                <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-semibold text-gray-800 truncate">
                        {product.name}
                    </h3>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        ID: {product.id}
                    </span>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {product.description}
                </p>

                <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold text-blue-600">
                        ${product.price}
                    </span>
                    <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                        Add to Cart
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ProductItem;
