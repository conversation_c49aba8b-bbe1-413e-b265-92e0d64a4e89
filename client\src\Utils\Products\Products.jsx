import React, { useEffect, useState } from "react";
import { collection, getDocs } from "firebase/firestore"
import { db } from "../../Config/firebase";

const Products = () => {
    const [productsList, setProductsList] = useState([])
    useEffect(() => {
        const fetchProducts = async () => {
            try {
                const querySnapshot = await getDocs(collection(db, "products"));
                const products = [];
                querySnapshot.forEach((doc) => {
                    products.push({ id: doc.id, ...doc.data() });
                });
                setProductsList(products);
            } catch (err) {
                console.log(err)
           }
        }
        fetchProducts()
    }, [])
    return (
        <>
            {productsList.map((product) => (
                <div key={product.id}>
                    <h1>{product.name}</h1>
                    <p>{product.description}</p>
                    <p>{product.price}</p>
                </div>
            ))}
        </>
    )
}

export default Products;
